import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:untitled1/resourses/app_colors.dart';
import 'package:dropdown_search/dropdown_search.dart';
import '../../resourses/resourses.dart';

class CompanyName {
  static int? companyId;
}

class CompanyNameDropdown extends StatefulWidget {
  final String? initialValue;
  final int? leadId;

  const CompanyNameDropdown({
    Key? key,
    this.initialValue,
    this.leadId, // Add this to constructor
  }) : super(key: key);

  @override
  _CompanyNameDropdownState createState() => _CompanyNameDropdownState();
}

class _CompanyNameDropdownState extends State<CompanyNameDropdown> {
  List<dynamic> _totalLeadList = [];
  bool isLoading = true;

  String? _selectedCompanyName;
  int? _selectedCompanyId;

  @override
  void initState() {
    super.initState();
    getCompanyName();
  }

  Future<void> getCompanyName() async {
    try {
      SharedPreferences sharedPreferences =
          await SharedPreferences.getInstance();
      String? token = sharedPreferences.getString("token");
      String? userId = sharedPreferences.getString("id");

      final response = await http.post(
        Uri.parse("https://crm.ihelpbd.com/api/crm-lead-data-show"),
        headers: {
          'Authorization': 'Bearer $token',
          'user_id': '$userId',
        },
        body: {
          'start_date': '',
          'end_date': '',
          'user_id_search': userId,
          'session_user_id': userId,
          'lead_pipeline_id': '',
          'lead_source_id': '',
          'searchData': '',
          'is_type': '0',
        },
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        setState(() {
          _totalLeadList = responseData['data'];

          // If leadId is provided, find and select the matching company
          if (widget.leadId != null) {
            final companyWithLeadId = _totalLeadList.firstWhere(
              (company) => company['id'] == widget.leadId,
              orElse: () => null,
            );

            if (companyWithLeadId != null) {
              _onCompanySelected(companyWithLeadId);
            }
          }
          // Fallback to initialValue if leadId doesn't match
          else if (widget.initialValue != null) {
            final initialCompany = _totalLeadList.firstWhere(
              (company) => company['company_name'] == widget.initialValue,
              orElse: () => null,
            );
            if (initialCompany != null) {
              _onCompanySelected(initialCompany);
            }
          }
        });
      } else {
        print('Failed to fetch Company data');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print("Error fetching company data: $e");
    }
  }

  void _onCompanySelected(dynamic selectedCompany) {
    setState(() {
      _selectedCompanyName = selectedCompany['company_name'];
      _selectedCompanyId = selectedCompany['id'];
      // Store the selected pipeline ID in the static variable
      CompanyName.companyId = _selectedCompanyId;
    });

    // Print the selected pipeline ID stored in the static variable
    print('Selected company name (static): ${CompanyName.companyId}');
  }

  @override
  Widget build(BuildContext context) {
    return _totalLeadList.isNotEmpty
        ? Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 3,
                  offset: const Offset(0, 1), // changes position of shadow
                ),
              ],
            ),
            child: DropdownSearch<dynamic>(
              items: (filter, infiniteScrollProps) => _totalLeadList,
              itemAsString: (dynamic company) => company['company_name'],
              selectedItem: _selectedCompanyName != null
                  ? _totalLeadList.firstWhere(
                      (company) =>
                          company['company_name'] == _selectedCompanyName,
                      orElse: () => null,
                    )
                  : null,
              onChanged: _onCompanySelected,
              validator: (value) =>
                  value == null ? 'Company name is required' : null,
              decoratorProps: DropDownDecoratorProps(
                decoration: InputDecoration(
                  hintText: "Select company name",
                  hintStyle: TextStyle(color: Colors.grey[400]),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Color(0xFFF8F6F8)),
                  ),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 10),
                  fillColor: const Color(0xFFF8F6F8),
                  suffixIcon: const Icon(Icons.keyboard_arrow_down_sharp,
                      size: 30, color: Colors.blue),
                ),
              ),
              popupProps: PopupProps.menu(
                showSearchBox: true,
                searchFieldProps: const TextFieldProps(
                  decoration: InputDecoration(
                    hintText: "Search company name...",
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                  ),
                ),
                menuProps: MenuProps(
                  backgroundColor: backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  elevation: 8,
                ),
                itemBuilder: (context, item, isSelected, isHighlighted) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isHighlighted ? Colors.blue.withOpacity(0.1) : null,
                    ),
                    child: Text(
                      item['company_name'],
                      style: TextStyle(
                        color: isSelected ? Colors.blue : Colors.black,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  );
                },
                fit: FlexFit.loose,
                constraints: const BoxConstraints(maxHeight: 400),
              ),
            ),
          )
        : R.appSpinKits.spinKitFadingCube;
  }
}
